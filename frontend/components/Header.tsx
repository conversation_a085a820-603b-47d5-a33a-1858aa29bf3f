'use client'

import { useState } from 'react'

import { ConnectionStatus } from './ConnectionStatus'
import { cn } from '@/lib/utils'

interface HeaderProps {
  title?: string
  subtitle?: string
}

export function Header({ title, subtitle }: HeaderProps) {

  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 z-40 backdrop-blur-sm bg-white/95">
      <div className="flex items-center justify-between px-6 py-4">
        {/* 标题区域 */}
        <div className="flex-1">
          {title && (
            <div>
              <h1 className="text-lg font-semibold text-gray-900">
                {title}
              </h1>
              {subtitle && (
                <p className="text-sm text-gray-500 mt-0.5">
                  {subtitle}
                </p>
              )}
            </div>
          )}
        </div>

        {/* 右侧操作区 */}
        <div className="flex items-center space-x-4">
          {/* 连接状态 */}
          <ConnectionStatus />
        </div>
      </div>
    </header>
  )
}
