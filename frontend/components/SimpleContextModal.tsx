'use client'

import { useState, useEffect } from 'react'
import { X, FileText, Search } from 'lucide-react'
import { Button } from './ui/Button'
import { Badge } from './ui/Badge'
import { Loading } from './ui/Loading'
import { apiMethods } from '@/lib/api'
import toast from 'react-hot-toast'

interface SimpleContextModalProps {
  isOpen: boolean
  onClose: () => void
  item: {
    analysis_id: string
    keyword: string
    company_name: string
    stock_code: string
    file_name: string
    count: number
  } | null
}

export function SimpleContextModal({ isOpen, onClose, item }: SimpleContextModalProps) {
  const [contextData, setContextData] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (isOpen && item) {
      fetchContext()
    }
  }, [isOpen, item])

  const fetchContext = async () => {
    if (!item) return

    setLoading(true)
    try {
      console.log('🔍 获取上下文:', item)
      
      const response = await apiMethods.getKeywordContext({
        analysis_id: item.analysis_id,
        keyword: item.keyword,
        context_length: 200,
        stock_code_filter: item.stock_code,
        file_name_filter: item.file_name
      })

      console.log('📥 上下文响应:', response)

      if (response.data.success) {
        setContextData(response.data.data)
      } else {
        toast.error(response.data.message || '获取上下文失败')
      }
    } catch (error: any) {
      console.error('❌ 获取上下文失败:', error)
      toast.error('获取上下文失败: ' + (error.response?.data?.message || error.message))
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setContextData(null)
    onClose()
  }

  if (!isOpen || !item) return null

  const contexts = contextData?.contexts || []

  return (
    <>
      {/* 背景遮罩 */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-50"
        onClick={handleClose}
      />
      
      {/* 模态框 */}
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 pointer-events-none">
        <div 
          className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col pointer-events-auto"
          onClick={(e) => e.stopPropagation()}
        >
          {/* 头部 */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50 rounded-t-lg">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                关键词上下文: "{item.keyword}"
              </h3>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span>公司: <strong>{item.company_name}</strong></span>
                <span>文件: <strong>{item.file_name}</strong></span>
                <Badge variant="success">出现 {item.count} 次</Badge>
              </div>
            </div>
            <Button variant="ghost" size="sm" onClick={handleClose}>
              <X className="w-5 h-5" />
            </Button>
          </div>

          {/* 内容区域 */}
          <div className="flex-1 overflow-y-auto p-6">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <Loading size="lg" text="正在获取上下文..." />
              </div>
            ) : contexts.length > 0 ? (
              <div className="space-y-6">
                {/* 统计信息 */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center space-x-4">
                    <FileText className="w-5 h-5 text-blue-600" />
                    <span className="font-medium text-blue-900">
                      找到 {contexts.length} 个相关文件，
                      总计 {contexts.reduce((sum: number, ctx: any) => sum + (ctx.total_count || 0), 0)} 处匹配
                    </span>
                  </div>
                </div>

                {/* 上下文列表 */}
                <div className="space-y-4">
                  {contexts.map((context: any, index: number) => (
                    <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
                      <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <FileText className="w-4 h-4 text-gray-600" />
                            <span className="font-medium text-gray-900">
                              {context.file_name || '未知文件'}
                            </span>
                          </div>
                          <Badge variant="default" size="sm">
                            {context.total_count || 0} 处匹配
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="p-4">
                        {context.snippets && context.snippets.length > 0 ? (
                          <div className="space-y-3">
                            {context.snippets.slice(0, 5).map((snippet: string, snippetIndex: number) => (
                              <div 
                                key={snippetIndex}
                                className="bg-yellow-50 border-l-4 border-yellow-400 p-3 rounded-r"
                              >
                                <div 
                                  className="text-sm text-gray-700 leading-relaxed"
                                  dangerouslySetInnerHTML={{ 
                                    __html: snippet.replace(
                                      new RegExp(`(${item.keyword})`, 'gi'),
                                      '<mark class="bg-yellow-200 px-1 rounded font-medium">$1</mark>'
                                    )
                                  }}
                                />
                              </div>
                            ))}
                            {context.snippets.length > 5 && (
                              <div className="text-center">
                                <span className="text-xs text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                                  还有 {context.snippets.length - 5} 个上下文片段...
                                </span>
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="text-center py-4 text-gray-500">
                            未找到具体的上下文片段
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-12">
                <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  未找到相关上下文
                </h3>
                <p className="text-gray-500">
                  关键词 "{item.keyword}" 在该文件中可能没有具体的上下文信息
                </p>
              </div>
            )}
          </div>

          {/* 底部 */}
          <div className="flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50 rounded-b-lg">
            <div className="text-sm text-gray-600">
              分析ID: {item.analysis_id}
            </div>
            <Button variant="secondary" onClick={handleClose}>
              关闭
            </Button>
          </div>
        </div>
      </div>
    </>
  )
}
