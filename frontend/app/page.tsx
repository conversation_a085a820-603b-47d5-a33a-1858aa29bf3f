'use client'

import { useState } from 'react'
import { AnalysisForm } from '@/components/AnalysisForm'
import { ResultsDisplay } from '@/components/ResultsDisplay'
import { TaskProgress } from '@/components/TaskProgress'
import { Header } from '@/components/Header'
import { Sidebar } from '@/components/Sidebar'
import { MobileNav } from '@/components/MobileNav'
import { AIAnalysis } from '@/components/AIAnalysis'
import { DataImport } from '@/components/DataImport'

import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Bot, Upload, FileText, Database, TrendingUp, Users } from 'lucide-react'

export default function Home() {
  const [activeTab, setActiveTab] = useState('analysis')
  const [currentTask, setCurrentTask] = useState<string | null>(null)
  const [analysisResults, setAnalysisResults] = useState<any>(null)

  const handleTaskStart = (taskId: string) => {
    setCurrentTask(taskId)
    setAnalysisResults(null)
  }

  const handleTaskComplete = (results: any) => {
    console.log('🎉 在线分析任务完成，原始结果:', results)

    // 在清除currentTask之前保存task_id
    const taskId = currentTask?.id
    setCurrentTask(null)

    // 检查结果格式并转换
    if (results && typeof results === 'object') {
      // 如果结果已经是数组格式（已转换过的），直接使用
      if (Array.isArray(results)) {
        console.log('📊 结果已是数组格式，直接使用:', results)
        setAnalysisResults(results)
        return
      }

      // 如果结果是对象格式，需要转换为数组格式
      const flattenedResults = []

      // 处理嵌套的分析结果格式
      const analysisData = results.analysis_results || results.data || results
      console.log('📊 分析数据:', analysisData)

      // 获取正确的analysis_id，优先使用保存的task_id
      const analysisId = taskId || results.analysis_id || results.task_id || 'online_analysis'
      console.log('🆔 使用的analysis_id:', analysisId)

      if (analysisData && typeof analysisData === 'object') {
        for (const [stockCode, files] of Object.entries(analysisData)) {
          if (files && typeof files === 'object') {
            for (const [fileName, keywords] of Object.entries(files as any)) {
              if (keywords && typeof keywords === 'object') {
                for (const [keyword, count] of Object.entries(keywords as any)) {
                  flattenedResults.push({
                    stock_code: stockCode,
                    company_name: stockCode, // 临时使用股票代码
                    file_name: fileName,
                    keyword: keyword,
                    count: typeof count === 'number' ? count : parseInt(count) || 0,
                    analysis_id: analysisId
                  })
                }
              }
            }
          }
        }
      }

      console.log('📊 转换后的在线分析结果:', flattenedResults)

      if (flattenedResults.length > 0) {
        setAnalysisResults(flattenedResults)
      } else {
        console.warn('⚠️ 在线分析结果转换后为空')
        setAnalysisResults([])
      }
    } else {
      console.warn('⚠️ 在线分析结果格式异常:', results)
      setAnalysisResults([])
    }
  }

  const handleAnalysisComplete = (results: any) => {
    setAnalysisResults(results)
  }

  const getPageTitle = () => {
    switch (activeTab) {
      case 'analysis':
        return '在线年报分析'
      case 'keyword':
        return '本地关键词分析'
      case 'ai':
        return 'AI 智能分析'
      case 'import':
        return '数据导入管理'
      default:
        return '年报分析工具'
    }
  }

  const getPageSubtitle = () => {
    switch (activeTab) {
      case 'analysis':
        return '从巨潮资讯网爬取并分析年报数据'
      case 'keyword':
        return '分析本地数据库中的年报文件'
      case 'ai':
        return '使用AI技术进行深度分析和洞察'
      case 'import':
        return '管理和导入本地TXT文件到数据库'
      default:
        return '智能年报分析与关键词统计'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* 桌面端侧边栏 */}
      <div className="hidden lg:block">
        <Sidebar activeTab={activeTab} onTabChange={setActiveTab} />
      </div>

      {/* 移动端导航 */}
      <MobileNav activeTab={activeTab} onTabChange={setActiveTab} />

      {/* 主内容区 */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* 桌面端头部 */}
        <div className="hidden lg:block">
          <Header title={getPageTitle()} subtitle={getPageSubtitle()} />
        </div>

        <main className="flex-1 p-4 lg:p-6 pb-20 lg:pb-6">
          <div className="max-w-7xl mx-auto">
            {/* 任务进度 */}
            {currentTask && (
              <div className="mb-6">
                <TaskProgress
                  taskId={currentTask}
                  onComplete={handleTaskComplete}
                />
              </div>
            )}

            {/* 内容区域 */}
            {activeTab === 'analysis' && (
              <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
                <div className="space-y-6">
                  <AnalysisForm
                    mode="online"
                    onTaskStart={handleTaskStart}
                    onAnalysisComplete={handleAnalysisComplete}
                  />
                </div>
                <div className="space-y-6">
                  {analysisResults ? (
                    <ResultsDisplay results={analysisResults} />
                  ) : (
                    <Card>
                      <CardContent className="flex flex-col items-center justify-center py-12 text-center">
                        <TrendingUp className="w-12 h-12 text-gray-400 mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                          等待分析结果
                        </h3>
                        <p className="text-gray-500 max-w-sm">
                          请在左侧配置分析参数并开始分析，结果将在这里显示
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'keyword' && (
              <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
                <div className="space-y-6">
                  <AnalysisForm
                    mode="local"
                    onTaskStart={handleTaskStart}
                    onAnalysisComplete={handleAnalysisComplete}
                  />
                </div>
                <div className="space-y-6">
                  {analysisResults ? (
                    <ResultsDisplay results={analysisResults} />
                  ) : (
                    <Card>
                      <CardContent className="flex flex-col items-center justify-center py-12 text-center">
                        <Database className="w-12 h-12 text-gray-400 mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                          等待分析结果
                        </h3>
                        <p className="text-gray-500 max-w-sm">
                          请在左侧配置关键词分析参数，结果将在这里显示
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'ai' && (
              <AIAnalysis />
            )}

            {activeTab === 'import' && (
              <DataImport />
            )}
          </div>
        </main>
      </div>
    </div>
  )
}
