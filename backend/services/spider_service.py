"""
爬虫服务模块
"""
import requests
import random
import time
import os
import re
import pdfplumber
from typing import List, Dict, Optional, Tuple, Callable
from models.database import DatabaseManager


class SpiderService:
    """爬虫服务类"""
    
    def __init__(self, db_manager: DatabaseManager):
        """初始化爬虫服务"""
        self.db = db_manager
        self.is_running = False
        
        # 用户代理列表
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        ]
        
        # 请求头
        self.headers = {
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Accept-Encoding': 'gzip, deflate',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Host': 'www.cninfo.com.cn',
            'Origin': 'http://www.cninfo.com.cn',
            'Referer': 'http://www.cninfo.com.cn/new/commonUrl?url=disclosure/list/notice',
            'X-Requested-With': 'XMLHttpRequest'
        }
        
        # API URLs
        self.orgid_url = 'http://www.cninfo.com.cn/new/data/szse_stock.json'
        self.query_url = 'http://www.cninfo.com.cn/new/hisAnnouncement/query'
        self.download_base_url = 'http://static.cninfo.com.cn/'
    
    def get_orgid_by_code(self, stock_code: str) -> Optional[Dict]:
        """根据股票代码获取orgId"""
        try:
            response = requests.get(self.orgid_url, headers=self.headers, timeout=10)
            if response.status_code == 200:
                data = response.json()
                stock_lists = data.get('stockList', [])
                for stock_info in stock_lists:
                    if stock_info.get('code') == stock_code:
                        return {
                            'code': stock_info['code'],
                            'orgId': stock_info['orgId'],
                            'zwjc': stock_info.get('zwjc', ''),
                        }
            return None
        except Exception as e:
            print(f"获取orgId失败: {e}")
            return None
    
    def search_announcements(self, stock_code: str, org_id: str, 
                           search_keyword: str = "年度报告", 
                           start_date: str = "2024-01-01", 
                           end_date: str = "2025-12-31") -> List[Dict]:
        """搜索公告"""
        try:
            self.headers['User-Agent'] = random.choice(self.user_agents)
            
            # 深市查询
            szse_query = {
                'pageNum': 1,
                'pageSize': 30,
                'tabName': 'fulltext',
                'column': 'szse',
                'stock': f'{stock_code},{org_id}',
                'searchkey': '',
                'secid': '',
                'plate': 'sz',
                'category': 'category_ndbg_szsh',
                'trade': '',
                'seDate': f'{start_date}~{end_date}',
                'sortName': '',
                'sortType': '',
                'isHLtitle': 'true'
            }
            
            # 沪市查询
            sse_query = szse_query.copy()
            sse_query.update({
                'column': 'sse',
                'plate': 'sh'
            })
            
            announcements = []
            
            # 查询深市
            try:
                print(f"  🔍 查询深市数据...")
                response = requests.post(self.query_url, data=szse_query, headers=self.headers, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('announcements'):
                        announcements.extend(data['announcements'])
                        print(f"    ✅ 深市找到 {len(data['announcements'])} 条公告")
                    else:
                        print(f"    ⚠️ 深市未找到公告")
                time.sleep(1)
            except Exception as e:
                print(f"    ❌ 深市查询失败: {e}")
            
            # 查询沪市
            try:
                print(f"  🔍 查询沪市数据...")
                response = requests.post(self.query_url, data=sse_query, headers=self.headers, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('announcements'):
                        announcements.extend(data['announcements'])
                        print(f"    ✅ 沪市找到 {len(data['announcements'])} 条公告")
                    else:
                        print(f"    ⚠️ 沪市未找到公告")
                time.sleep(1)
            except Exception as e:
                print(f"    ❌ 沪市查询失败: {e}")
            
            return announcements
            
        except Exception as e:
            print(f"搜索公告失败: {e}")
            return []
    
    def download_pdf(self, announcement: Dict, pdf_dir: str = "pdf") -> Optional[str]:
        """下载PDF文件"""
        try:
            if not os.path.exists(pdf_dir):
                os.makedirs(pdf_dir)
            
            adj_url = announcement.get('adjunctUrl', '')
            if not adj_url:
                return None
            
            download_url = self.download_base_url + adj_url
            filename = f"{announcement.get('secCode', 'unknown')}_{announcement.get('secName', 'unknown')}_{announcement.get('announcementTitle', 'unknown')}.pdf"
            
            # 清理文件名中的非法字符
            filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
            filepath = os.path.join(pdf_dir, filename)
            
            # 检查文件是否已存在
            if os.path.exists(filepath):
                print(f"    📄 PDF文件已存在: {filename}")
                return filepath
            
            print(f"    📥 下载PDF: {filename}")
            response = requests.get(download_url, headers=self.headers, timeout=30)
            
            if response.status_code == 200:
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                print(f"    ✅ PDF下载成功: {filename}")
                return filepath
            else:
                print(f"    ❌ PDF下载失败: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            print(f"    ❌ PDF下载异常: {e}")
            return None
    
    def pdf_to_text(self, pdf_path: str, txt_dir: str = "txt") -> Optional[str]:
        """PDF转文本"""
        try:
            if not os.path.exists(txt_dir):
                os.makedirs(txt_dir)
            
            # 生成TXT文件路径
            pdf_filename = os.path.basename(pdf_path)
            txt_filename = pdf_filename.replace('.pdf', '.txt')
            txt_path = os.path.join(txt_dir, txt_filename)
            
            # 检查TXT文件是否已存在
            if os.path.exists(txt_path):
                print(f"    📄 TXT文件已存在: {txt_filename}")
                return txt_path
            
            print(f"    🔄 转换PDF到TXT: {txt_filename}")
            
            text_content = ""
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text_content += page_text + "\n"
            
            if text_content.strip():
                with open(txt_path, 'w', encoding='utf-8') as f:
                    f.write(text_content)
                print(f"    ✅ TXT转换成功: {txt_filename}")
                return txt_path
            else:
                print(f"    ⚠️ PDF内容为空")
                return None
                
        except Exception as e:
            print(f"    ❌ PDF转换失败: {e}")
            return None
    
    def stop_crawling(self):
        """停止爬虫"""
        self.is_running = False
    
    def crawl_and_analyze(self, stock_codes: List[str], keywords: List[str],
                         search_keyword: str = "年度报告",
                         start_date: str = "2024-01-01",
                         end_date: str = "2025-12-31",
                         use_online: bool = True,
                         task_id: str = None,
                         related_parties: List[str] = None,
                         innovation_keywords: List[str] = None,
                         progress_callback: Callable = None) -> Dict:
        """爬取并分析年报"""
        
        self.is_running = True
        results = {
            'analysis_results': {},
            'related_party_analysis': {},
            'download_results': {},
            'summary': {
                'total_companies': len(stock_codes),
                'successful_downloads': 0,
                'failed_downloads': 0,
                'total_keywords_found': 0
            }
        }
        
        try:
            for i, stock_code in enumerate(stock_codes):
                if not self.is_running:
                    break
                
                if progress_callback:
                    progress_callback(i, len(stock_codes), f"处理股票代码: {stock_code}")
                
                print(f"\n[{i+1}/{len(stock_codes)}] 处理股票代码: {stock_code}")
                
                # 获取orgId
                org_info = self.get_orgid_by_code(stock_code)
                if not org_info:
                    print(f"  ❌ 未找到股票代码 {stock_code} 的orgId")
                    continue
                
                company_name = org_info.get('zwjc', stock_code)
                org_id = org_info['orgId']
                
                # 添加公司信息到数据库
                self.db.add_company(stock_code, company_name, org_id)
                
                if use_online:
                    # 在线搜索和下载
                    announcements = self.search_announcements(
                        stock_code, org_id, search_keyword, start_date, end_date
                    )
                    
                    if not announcements:
                        print(f"  ⚠️ 未找到相关公告")
                        continue
                    
                    # 处理公告
                    for announcement in announcements:
                        if not self.is_running:
                            break
                        
                        # 下载PDF
                        pdf_path = self.download_pdf(announcement)
                        if not pdf_path:
                            continue
                        
                        # 转换为TXT
                        txt_path = self.pdf_to_text(pdf_path)
                        if not txt_path:
                            continue
                        
                        # 读取TXT内容
                        try:
                            with open(txt_path, 'r', encoding='utf-8') as f:
                                txt_content = f.read()
                        except Exception as e:
                            print(f"    ❌ 读取TXT文件失败: {e}")
                            continue
                        
                        # 保存到数据库
                        report_title = announcement.get('announcementTitle', '')
                        file_name = os.path.basename(txt_path)
                        
                        self.db.add_report(
                            stock_code=stock_code,
                            company_name=company_name,
                            report_title=report_title,
                            file_name=file_name,
                            file_path=txt_path,
                            txt_content=txt_content
                        )
                        
                        # 关键词分析
                        keyword_stats = self.analyze_keywords(txt_content, keywords)

                        # 保存分析结果到内存
                        if stock_code not in results['analysis_results']:
                            results['analysis_results'][stock_code] = {}
                        results['analysis_results'][stock_code][file_name] = keyword_stats

                        # 保存分析结果到数据库（用于上下文查看）
                        if task_id:
                            # 获取报告ID
                            report_id = self.db.get_report_id_by_file_path(txt_path)
                            if report_id:
                                # 保存关键词分析结果（传递整个字典）
                                self.db.save_keyword_analysis(
                                    analysis_id=task_id,
                                    stock_code=stock_code,
                                    report_id=report_id,
                                    keyword_stats=keyword_stats
                                )
                        
                        # 关联方分析
                        if related_parties and innovation_keywords:
                            related_analysis = self.analyze_related_parties(
                                txt_content, related_parties, innovation_keywords
                            )
                            if related_analysis:
                                if stock_code not in results['related_party_analysis']:
                                    results['related_party_analysis'][stock_code] = {}
                                results['related_party_analysis'][stock_code][file_name] = related_analysis
                        
                        results['summary']['successful_downloads'] += 1
                        break  # 只处理第一个找到的年报
                
                else:
                    # 使用本地数据
                    reports = self.db.get_reports_by_stock_codes([stock_code])
                    for report in reports:
                        if not self.is_running:
                            break
                        
                        txt_content = report.get('txt_content', '')
                        if not txt_content:
                            continue
                        
                        file_name = report.get('file_name', '')
                        
                        # 关键词分析
                        keyword_stats = self.analyze_keywords(txt_content, keywords)
                        
                        # 保存分析结果
                        if stock_code not in results['analysis_results']:
                            results['analysis_results'][stock_code] = {}
                        results['analysis_results'][stock_code][file_name] = keyword_stats
                        
                        # 关联方分析
                        if related_parties and innovation_keywords:
                            related_analysis = self.analyze_related_parties(
                                txt_content, related_parties, innovation_keywords
                            )
                            if related_analysis:
                                if stock_code not in results['related_party_analysis']:
                                    results['related_party_analysis'][stock_code] = {}
                                results['related_party_analysis'][stock_code][file_name] = related_analysis
            
            if progress_callback:
                progress_callback(len(stock_codes), len(stock_codes), "分析完成")
            
            return results
            
        except Exception as e:
            print(f"❌ 爬取分析过程出错: {e}")
            raise e
    
    def analyze_keywords(self, text: str, keywords: List[str]) -> Dict[str, int]:
        """分析关键词出现次数"""
        # 清理文本，只保留中文字符
        clean_text = re.sub(r'[^\u4e00-\u9fa5]', '', text)
        
        keyword_stats = {}
        for keyword in keywords:
            count = clean_text.count(keyword)
            keyword_stats[keyword] = count
        
        return keyword_stats
    
    def analyze_related_parties(self, text: str, related_parties: List[str], 
                               innovation_keywords: List[str]) -> Dict:
        """分析关联方协同创新"""
        results = {}
        
        for party in related_parties:
            if party in text:
                # 找到关联方提及的上下文
                contexts = self.find_contexts_with_keywords(text, party, innovation_keywords)
                if contexts:
                    results[party] = {
                        'found': True,
                        'contexts': contexts,
                        'innovation_keywords_found': list(set(
                            kw for context in contexts for kw in context['keywords_found']
                        ))
                    }
                else:
                    results[party] = {
                        'found': True,
                        'contexts': [],
                        'innovation_keywords_found': []
                    }
            else:
                results[party] = {
                    'found': False,
                    'contexts': [],
                    'innovation_keywords_found': []
                }
        
        return results
    
    def find_contexts_with_keywords(self, text: str, party: str, 
                                   keywords: List[str], context_length: int = 200) -> List[Dict]:
        """查找包含关键词的上下文"""
        contexts = []
        
        # 找到所有关联方出现的位置
        party_positions = []
        start = 0
        while True:
            pos = text.find(party, start)
            if pos == -1:
                break
            party_positions.append(pos)
            start = pos + 1
        
        # 对每个位置检查周围是否有创新关键词
        for pos in party_positions:
            start_pos = max(0, pos - context_length)
            end_pos = min(len(text), pos + len(party) + context_length)
            context_text = text[start_pos:end_pos]
            
            # 检查上下文中的关键词
            keywords_found = []
            for keyword in keywords:
                if keyword in context_text:
                    keywords_found.append(keyword)
            
            if keywords_found:
                contexts.append({
                    'text': context_text,
                    'keywords_found': keywords_found,
                    'position': pos
                })
        
        return contexts
